using UnityEngine;
using UnityEngine.UI;
using TMPro;
using GridSystem;

public class ObjectPlacementUI : MonoBehaviour
{
    public GridManager gridManager;
    public GameObject buttonPrefab;
    public Transform buttonContainer;
    [System.NonSerialized] public PlaceableObjectData[] placeableObjects;
    public Image selectionIndicator; // Hazır seçili durum göstergesi
    public Button deleteButton; // Silme butonu referansı
    public Button deleteAllButton; // Tümünü silme butonu referansı

    private Button currentSelectedButton;
    private bool isDeleteMode = false; // Silme modu durumu

    private void Start()
    {
        ValidateComponents();
        LoadPlaceableObjectsFromResources();
        CreateObjectButtons();
        SetupDeleteButton();
        SetupDeleteAllButton();

        // Başlangıçta göstergeyi gizle
        if (selectionIndicator != null)
        {
            selectionIndicator.gameObject.SetActive(false);
        }
    }

    private void LoadPlaceableObjectsFromResources()
    {
        // Resources/SO klasöründen tüm PlaceableObjectData'ları yükle
        placeableObjects = Resources.LoadAll<PlaceableObjectData>("SO");

        if (placeableObjects == null || placeableObjects.Length == 0)
        {
            Debug.LogWarning("No PlaceableObjectData found in Resources/SO folder!");
            placeableObjects = new PlaceableObjectData[0];
        }
        else
        {
            Debug.Log($"Loaded {placeableObjects.Length} PlaceableObjectData from Resources/SO");
        }
    }

    private void ValidateComponents()
    {
        if (gridManager == null)
            Debug.LogError("GridSystem referansı eksik!");
        if (buttonPrefab == null)
            Debug.LogError("Button Prefab referansı eksik!");
        if (buttonContainer == null)
            Debug.LogError("Button Container referansı eksik!");
        if (placeableObjects == null || placeableObjects.Length == 0)
            Debug.LogError("Yerleştirilebilir objeler tanımlanmamış!");
        if (selectionIndicator == null)
            Debug.LogError("Selection Indicator referansı eksik!");
        if (deleteButton == null)
            Debug.LogError("Delete Button referansı eksik!");
    }

    private void SetupDeleteButton()
    {
        if (deleteButton != null)
        {
            deleteButton.onClick.AddListener(ToggleDeleteMode);

            // Silme butonu için görsel geri bildirim ekle
            Image deleteButtonImage = deleteButton.GetComponent<Image>();
            if (deleteButtonImage != null)
            {
                // Normal rengi sakla
                Color normalColor = deleteButtonImage.color;

                // Buton rengini değiştir fonksiyonu
                void UpdateDeleteButtonVisual()
                {
                    deleteButtonImage.color = isDeleteMode ? Color.red : normalColor;
                }

                // İlk rengi ayarla
                UpdateDeleteButtonVisual();

                // Click handler'a renk değişimini ekle
                deleteButton.onClick.AddListener(UpdateDeleteButtonVisual);
            }
        }
    }

    private void SetupDeleteAllButton()
    {
        if (deleteAllButton != null)
        {
            deleteAllButton.onClick.AddListener(DeleteAllObjects);
        }
        else
        {
            Debug.LogWarning("Delete All Button is not assigned!");
        }
    }

    private void DeleteAllObjects()
    {
        // EventManager ile tüm nesneleri silme event'i gönder
        EventManager.Invoke("DeleteAllObjects");
        Debug.Log("Delete All Objects event sent");
    }

    private void ToggleDeleteMode()
    {
        isDeleteMode = !isDeleteMode;

        if (isDeleteMode)
        {
            // Silme moduna geçerken seçili objeyi temizle
            UnselectCurrentButton();
            // ObjectSelectionCleared event'ini gönderme, sadece UI'ı temizle
        }

        // EventManager ile delete mode değişikliği event'i gönder
        EventManager.Invoke("DeleteModeChanged", isDeleteMode);
    }

    private void CreateObjectButtons()
    {
        foreach (var objectData in placeableObjects)
        {
            if (objectData == null)
            {
                Debug.LogError("Null PlaceableObjectData tespit edildi!");
                continue;
            }

            GameObject buttonObj = Instantiate(buttonPrefab, buttonContainer);
            buttonObj.gameObject.SetActive(true);
            Button button = buttonObj.GetComponent<Button>();

            // Find and set up the icon image
            Image[] allImages = buttonObj.GetComponentsInChildren<Image>(true);
            Debug.Log($"Button {objectData.objectName} has {allImages.Length} Image components");

            Image buttonImage = null;
            Image buttonBackground = button.GetComponent<Image>();

            // Button'un kendisi olmayan ilk Image'i bul (icon için)
            foreach (var img in allImages)
            {
                Debug.Log($"  Image: {img.name}, Same as button: {img == buttonBackground}");
                if (img != buttonBackground)
                {
                    buttonImage = img;
                    break;
                }
            }

            if (buttonImage != null)
            {
                buttonImage.gameObject.SetActive(true);
                if (objectData.icon != null)
                {
                    buttonImage.sprite = objectData.icon;
                    buttonImage.SetNativeSize();
                    Debug.Log($"Icon set for {objectData.objectName}: {objectData.icon.name} on {buttonImage.name}");
                }
                else
                {
                    Debug.LogWarning($"No icon found for {objectData.objectName}");
                }
            }
            else
            {
                Debug.LogWarning($"No icon Image component found for button {objectData.objectName}");
            }

            // Set button text
            TextMeshProUGUI buttonText = buttonObj.GetComponentInChildren<TextMeshProUGUI>(true);
            if (buttonText != null)
            {
                buttonText.gameObject.SetActive(true);
                buttonText.text = objectData.objectName;
            }

            // Click handler'ı ekle
            button.onClick.AddListener(() => ToggleObjectSelection(objectData, button));
        }
    }

    private void ToggleObjectSelection(PlaceableObjectData objectData, Button clickedButton)
    {
        if (isDeleteMode)
        {
            // Silme modunda iken obje seçimine izin verme
            return;
        }

        if (currentSelectedButton == clickedButton)
        {
            // Seçili butona tekrar tıklandı, seçimi kaldır
            UnselectCurrentButton();
            // Sadece EventManager kullan, gridManager çağrısını kaldır
            EventManager.Invoke("ObjectSelectionCleared");
        }
        else
        {
            // Önceki seçimi temizle
            UnselectCurrentButton();

            // Yeni butonu seç
            currentSelectedButton = clickedButton;

            // Seçili durum göstergesini butona taşı
            if (selectionIndicator != null)
            {
                selectionIndicator.transform.SetParent(clickedButton.transform);
                selectionIndicator.transform.SetAsLastSibling(); // En üstte görünsün

                // RectTransform ayarlarını yap
                RectTransform indicatorRect = selectionIndicator.GetComponent<RectTransform>();
                indicatorRect.anchorMin = new Vector2(1, 1); // Sağ üst köşe
                indicatorRect.anchorMax = new Vector2(1, 1);
                indicatorRect.pivot = new Vector2(1, 1);
                indicatorRect.anchoredPosition = Vector2.zero;

                selectionIndicator.gameObject.SetActive(true);
            }

            // Sadece EventManager kullan, gridManager çağrısını kaldır
            EventManager.Invoke("ObjectSelected", objectData);
        }
    }

    public bool IsDeleteModeActive()
    {
        return isDeleteMode;
    }

    private void UnselectCurrentButton()
    {
        if (currentSelectedButton != null)
        {
            if (selectionIndicator != null)
            {
                selectionIndicator.gameObject.SetActive(false);
            }
            currentSelectedButton = null;
        }
    }
}