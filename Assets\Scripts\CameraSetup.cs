using UnityEngine;
using GridSystem;

[ExecuteInEditMode]
public class CameraSetup : MonoBehaviour
{
    public GridManager gridManager;
    public float panSpeed = 0.01f;

    private Vector2 lastPanPosition;
    public bool IsPanning { get; private set; }

    private bool blockPanning = false;

    /// <summary>
    /// Sürükleme sırasında kamera kaymasını engelle.
    /// </summary>
    public void BlockPanningDuringDrag(bool block)
    {
        blockPanning = block;
    }

    void Start()
    {
        if (gridManager == null)
        {
            gridManager = FindObjectOfType<GridManager>();
        }
        CenterCameraOnGrid();
    }

    void OnValidate()
    {
        if (gridManager != null)
        {
            CenterCameraOnGrid();
        }
    }

    void Update()
    {
        if (Application.isPlaying)
        {
            HandlePanning();
        }
        else
        {
            CenterCameraOnGrid();
        }
    }

    void HandlePanning()
    {
        if (blockPanning) return;

        // Dokunmatik kontrol
        if (Input.touchCount > 0)
        {
            Touch touch = Input.GetTouch(0);

            if (touch.phase == TouchPhase.Began)
            {
                IsPanning = true;
                lastPanPosition = touch.position;
            }
            else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
            {
                IsPanning = false;
            }
            else if (touch.phase == TouchPhase.Moved && IsPanning)
            {
                PanCamera(touch.position);
            }
        }
        else // Mouse kontrol
        {
            if (Input.GetMouseButtonDown(0))
            {
                IsPanning = true;
                lastPanPosition = Input.mousePosition;
            }
            else if (Input.GetMouseButtonUp(0))
            {
                IsPanning = false;
            }
            else if (Input.GetMouseButton(0) && IsPanning)
            {
                PanCamera(Input.mousePosition);
            }
        }
    }

    void PanCamera(Vector2 newPosition)
    {
        Vector2 delta = newPosition - lastPanPosition;

        Vector3 movement = new Vector3(
            -delta.x * panSpeed,
            0f,
            -delta.y * panSpeed
        );

        // Kamera yönüne göre hareketi düzelt
        movement = Quaternion.Euler(0, transform.eulerAngles.y, 0) * movement;

        transform.position += movement;
        lastPanPosition = newPosition;
    }

   void CenterCameraOnGrid()
{
    if (gridManager == null) return;

    // Grid'in merkez hücresinin dünya pozisyonu
    Vector2Int centerGridPos = new Vector2Int(
        (gridManager.gridSize.x - 1) / 2,
        (gridManager.gridSize.y - 1) / 2
    );

    Vector3 gridCenter = gridManager.transform.position + new Vector3(
        (centerGridPos.x + 0.5f) * gridManager.cellSize,
        0f,
        (centerGridPos.y + 0.5f) * gridManager.cellSize
    );

    // Kamera yön vektörünü al (ileriye doğru bakış yönü)
    Vector3 forward = transform.forward;
    forward.y = 0; // Y bileşenini yok say (kameranın ileri düzlemindeki yön)

    forward.Normalize();

    // Kamera yüksekliği sabit (12), bu yüzden o yükseklikten geriye doğru bakıyoruz
    float height = transform.position.y;
    float angleXRad = transform.eulerAngles.x * Mathf.Deg2Rad;

    // Kamera merkezdeki hücreye baksın diye, yüksekliği ve açıya göre offset hesapla
    float distance = height / Mathf.Tan(angleXRad); // Eğik üçgende taban

    // Grid merkezinden ileri doğru bakış yönünde uzaklık kadar geri çık
    Vector3 cameraPos = gridCenter - forward * distance;

    // Y sabit kalsın
    cameraPos.y = transform.position.y;

    transform.position = cameraPos;
}


}
