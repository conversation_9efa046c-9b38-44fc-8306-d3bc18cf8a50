using UnityEngine;
using System.Collections.Generic;

namespace GridSystem
{
    public class GridRenderer : MonoBehaviour
    {
        private Camera mainCamera;
        private Material gridMaterial;
        private List<LineRenderer> gridLines = new List<LineRenderer>();

        [Header("Grid Visualization")]
        public Color gridColor = new Color(0.6f, 0.8f, 1f, 0.8f);

        public float gridLineWidth = 0.02f;
        public float gridLineHeight = 0.01f;
        public float updateInterval = 0.1f;
        public bool showGrid = true;

        private float nextUpdateTime;
        private GridManager gridManager;

        private Transform gridLineParent;

        private void Awake()
        {
            InitializeComponents();
        }

        private void InitializeComponents()
        {
            if (mainCamera == null) mainCamera = Camera.main;
            if (gridManager == null) gridManager = GetComponent<GridManager>();
            if (gridMaterial == null) CreateGridMaterial();
            if (gridLineParent == null) SetupGridLineParent();
        }

        private void SetupGridLineParent()
        {
            Transform existing = transform.Find("GridLineRender");
            if (existing != null)
            {
                gridLineParent = existing;
            }
            else
            {
                GameObject go = new GameObject("GridLineRender");
                gridLineParent = go.transform;
                gridLineParent.SetParent(transform);
                gridLineParent.localPosition = Vector3.zero;
            }
        }

        private void CreateGridMaterial()
        {
            gridMaterial = new Material(Shader.Find("Unlit/Color")); // Daha net görünüm için Unlit
            gridMaterial.color = gridColor;
        }

        private void Update()
        {
            if (!showGrid || mainCamera == null) return;

            if (Time.time >= nextUpdateTime)
            {
                UpdateVisibleGridLines();
                nextUpdateTime = Time.time + updateInterval;
            }
        }

        private void UpdateVisibleGridLines()
        {
            if (gridManager == null)
            {
                InitializeComponents();
                if (gridManager == null) return;
            }

            ClearGridLines();

            Vector3 gridStart = transform.position;
            Vector3 gridEnd = transform.position + new Vector3(gridManager.gridSize.x * gridManager.cellSize, 0, gridManager.gridSize.y * gridManager.cellSize);

            // Yatay çizgiler
            for (int z = 0; z <= gridManager.gridSize.y; z++)
            {
                Vector3 lineStart = gridStart + new Vector3(0, gridLineHeight, z * gridManager.cellSize);
                Vector3 lineEnd = new Vector3(gridEnd.x, gridLineHeight, lineStart.z);
                CreateGridLine(lineStart, lineEnd, $"HLine_{z}");
            }

            // Dikey çizgiler
            for (int x = 0; x <= gridManager.gridSize.x; x++)
            {
                Vector3 lineStart = gridStart + new Vector3(x * gridManager.cellSize, gridLineHeight, 0);
                Vector3 lineEnd = new Vector3(lineStart.x, gridLineHeight, gridEnd.z);
                CreateGridLine(lineStart, lineEnd, $"VLine_{x}");
            }
        }

        private void CreateGridLine(Vector3 start, Vector3 end, string name)
        {
            GameObject lineObj = new GameObject(name);
            lineObj.transform.SetParent(gridLineParent);
            lineObj.transform.localPosition = Vector3.zero;

            LineRenderer line = lineObj.AddComponent<LineRenderer>();
            line.material = gridMaterial;
            line.startColor = gridColor;
            line.endColor = gridColor;
            line.startWidth = gridLineWidth;
            line.endWidth = gridLineWidth;
            line.positionCount = 2;
            line.useWorldSpace = true;

            line.SetPosition(0, start);
            line.SetPosition(1, end);

            line.receiveShadows = false;
            line.shadowCastingMode = UnityEngine.Rendering.ShadowCastingMode.Off;

            gridLines.Add(line);
        }

        private void ClearGridLines()
        {
            if (gridLineParent != null)
            {
                foreach (Transform child in gridLineParent)
                {
                    DestroyImmediate(child.gameObject);
                }
            }
            gridLines.Clear();
        }

        private void OnDestroy()
        {
            ClearGridLines();
            if (gridMaterial != null)
            {
                DestroyImmediate(gridMaterial);
            }
        }

        private void OnDrawGizmos()
        {
            if (!Application.isPlaying && showGrid)
            {
                if (gridManager == null)
                {
                    gridManager = GetComponent<GridManager>();
                    if (gridManager == null) return;
                }
                DrawEditorGrid();
            }
        }

        private void DrawEditorGrid()
        {
            Gizmos.color = gridColor;
            Vector3 origin = transform.position;

            for (int z = 0; z <= gridManager.gridSize.y; z++)
            {
                Vector3 start = origin + new Vector3(0, gridLineHeight, z * gridManager.cellSize);
                Vector3 end = origin + new Vector3(gridManager.gridSize.x * gridManager.cellSize, gridLineHeight, z * gridManager.cellSize);
                Gizmos.DrawLine(start, end);
            }

            for (int x = 0; x <= gridManager.gridSize.x; x++)
            {
                Vector3 start = origin + new Vector3(x * gridManager.cellSize, gridLineHeight, 0);
                Vector3 end = origin + new Vector3(x * gridManager.cellSize, gridLineHeight, gridManager.gridSize.y * gridManager.cellSize);
                Gizmos.DrawLine(start, end);
            }
        }

        private void OnValidate()
        {
            // OnValidate sırasında GameObject oluşturmayı engelle
            if (Application.isPlaying && gridManager != null)
            {
                InitializeComponents();
                // Bir sonraki frame'de güncelle
                StartCoroutine(UpdateGridNextFrame());
            }
        }

        private System.Collections.IEnumerator UpdateGridNextFrame()
        {
            yield return null; // Bir frame bekle
            if (gridManager != null)
            {
                UpdateVisibleGridLines();
            }
        }
    }
}
