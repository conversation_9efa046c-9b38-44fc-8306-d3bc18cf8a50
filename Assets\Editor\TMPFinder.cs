using UnityEditor;
using UnityEngine;
using TMPro;
using System.Collections.Generic;

public class TMPFinder : EditorWindow
{
    [MenuItem("Tools/Select All TMP Components in Scene")]
    public static void ShowWindow()
    {
        GetWindow<TMPFinder>("TMP Finder");
    }

    private void OnGUI()
    {
        if (GUILayout.Button("Find and Select All TMP Objects"))
        {
            SelectTMPObjectsInScene();
        }
    }

    private void SelectTMPObjectsInScene()
    {
        List<GameObject> foundObjects = new List<GameObject>();

        // Hem UGUI hem 3D TextMeshPro bileşenlerini ara
        TextMeshProUGUI[] tmpUGUIs = FindObjectsOfType<TextMeshProUGUI>(true);
        TextMeshPro[] tmpTexts = FindObjectsOfType<TextMeshPro>(true);

        foreach (var tmp in tmpUGUIs)
        {
            foundObjects.Add(tmp.gameObject);
        }

        foreach (var tmp in tmpTexts)
        {
            foundObjects.Add(tmp.gameObject);
        }

        if (foundObjects.Count > 0)
        {
            Selection.objects = foundObjects.ToArray(); // Objeleri seç
            Debug.Log($"✅ {foundObjects.Count} TextMeshPro bileşenli obje seçildi.");
        }
        else
        {
            Debug.LogWarning("⚠️ Sahnedeki hiçbir obje TextMeshPro bileşeni içermiyor.");
        }
    }
}
