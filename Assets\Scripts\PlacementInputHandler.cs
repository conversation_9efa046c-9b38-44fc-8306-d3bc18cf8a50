using UnityEngine;
using GridSystem;
using UnityEngine.EventSystems;
using System.Collections.Generic;

public class PlacementInputHandler : MonoBehaviour
{
    public Camera mainCamera;
    public LayerMask groundLayer;
    public ObjectPlacementUI placementUI;
    
    private CameraSetup cameraSetup;
    private bool isDragging = false;
    private Vector2 dragStartPosition;
    private const float dragThreshold = 5f;
    private EventSystem eventSystem;
    private PooledObject currentHoverObject;
    private PooledObject draggedObject;
    private GridPlacementSystem originalGrid;
    private Vector3 dragOffset;
    private Vector2Int originalGridPosition;

    // Multi-place sistemi için ye<PERSON>
    private bool isMultiPlaceMode = false;
    private GridPlacementSystem multiPlaceGrid;
    private HashSet<Vector2Int> multiPlacedCells = new HashSet<Vector2Int>();
    private PlaceableObjectData multiPlaceObjectData;

    // Multi-place delay sistemi
    private bool isWaitingForMultiPlace = false;
    private float multiPlaceDelay = 0.3f; // 300ms delay
    private float multiPlaceTimer = 0f;
    private GridPlacementSystem pendingMultiPlaceGrid;
    private Vector2Int pendingMultiPlacePosition;

    private void Start()
    {
        if (mainCamera == null)
        {
            mainCamera = Camera.main;
        }
        
        cameraSetup = mainCamera.GetComponent<CameraSetup>();
        if (cameraSetup == null)
        {
            Debug.LogError("CameraSetup component is missing on the main camera!");
        }

        eventSystem = EventSystem.current;
        if (eventSystem == null)
        {
            Debug.LogError("EventSystem not found in the scene!");
        }
    }

    private void Update()
    {
        if (IsPointerOverUI())
        {
            isDragging = false;
            ClearHover();
            return;
        }

        HandleHover();

        if (Input.GetMouseButtonDown(0))
        {
            // Silme modunda taşıma işlemini engelle
            bool isDeleteMode = placementUI != null && placementUI.IsDeleteModeActive();
            Debug.Log($"Mouse down - DeleteMode: {isDeleteMode}");

            isDragging = true;
            dragStartPosition = Input.mousePosition;

            if (!isDeleteMode)
            {

                // Grid üzerinden nesne seçimi
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                {
                    // Hangi grid sistemine tıklandığını bul
                    GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    if (hitGrid != null)
                    {
                        Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                        draggedObject = hitGrid.GetObjectAtCell(gridPos);
                        if (draggedObject != null)
                        {
                            // Nesnenin gerçek grid'ini object data'dan al
                            PlaceableObjectData objectData = draggedObject.GetObjectData();
                            if (objectData != null && objectData.currentGrid != null)
                            {
                                originalGrid = objectData.currentGrid;
                            }
                            else
                            {
                                originalGrid = hitGrid;
                            }

                            originalGridPosition = draggedObject.GetGridPosition();
                            dragOffset = draggedObject.transform.position - hit.point;
                            Debug.Log("Nesne seçildi: " + draggedObject.name + " Grid: " + originalGrid.name);

                            // EventManager ile taşıma başladı event'i gönder
                            EventManager.Invoke("ObjectMoveStarted", draggedObject);
                        }
                        else
                        {
                            // Multi-place modunu başlat (boş grid cell'e tıklandı)
                            StartMultiPlaceMode(hitGrid, gridPos);
                        }
                    }
                }
            }
        }
        else if (Input.GetMouseButton(0) && (draggedObject != null || isMultiPlaceMode))
        {
            // Silme modunda taşıma işlemini engelle
            bool isDeleteMode = placementUI != null && placementUI.IsDeleteModeActive();
            if (!isDeleteMode)
            {
                if (isMultiPlaceMode)
                {
                    // Multi-place modu - mouse hareket ettikçe obje yerleştir
                    HandleMultiPlaceDrag();
                }
                else if (draggedObject != null)
                {
                    // Normal drag modu - nesneyi sürükle
                    Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                    if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                    {
                        // Hangi grid sistemine sürüklendiğini bul
                        GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                        if (hitGrid != null)
                        {
                            // Grid hücresine snap yap
                            Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                            Vector3 snappedPosition = hitGrid.GridToWorldPosition(gridPos);
                            // Y eksenini koru
                            snappedPosition.y = draggedObject.transform.position.y;
                            draggedObject.transform.position = snappedPosition;
                        }
                    }
                }

                // Camera pan'ı engellemek için flag set et
                if (cameraSetup != null)
                {
                    cameraSetup.BlockPanningDuringDrag(true);
                }
            }
        }
        else if (Input.GetMouseButtonUp(0))
        {
            Debug.Log($"Mouse up - isDragging: {isDragging}");
            if (!isDragging) return;

            // Sürükleme bittiğinde pan engelini kaldır
            if (cameraSetup != null)
            {
                cameraSetup.BlockPanningDuringDrag(false);
            }

            // Silme modunda taşıma işlemini engelle
            bool isDeleteMode = placementUI != null && placementUI.IsDeleteModeActive();
            Debug.Log($"Mouse up - DeleteMode: {isDeleteMode}, DraggedObject: {(draggedObject != null ? draggedObject.name : "null")}");

            if (isMultiPlaceMode && !isDeleteMode)
            {
                // Multi-place modunu bitir
                EndMultiPlaceMode();
            }
            else if (draggedObject != null && !isDeleteMode)
            {
                bool placementSuccessful = false;

                // Try to place on new grid
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                {
                    // Find the target grid system
                    GridPlacementSystem targetGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    if (targetGrid != null)
                    {
                        Vector2Int targetGridPos = targetGrid.GetGridPositionFromWorld(hit.point);
                        float originalY = draggedObject.transform.position.y;

                        if (targetGrid != originalGrid)
                        {
                            // Farklı bir gride taşıma
                            if (targetGrid.GetObjectAtCell(targetGridPos) == null)
                            {
                                // Nesnenin object data'sını al
                                PlaceableObjectData objectData = draggedObject.GetObjectData();

                                // Orijinal griddeki nesneyi sil
                                originalGrid.TryRemoveObject(draggedObject.transform.position);

                                // Hedef gride yerleştir
                                targetGrid.SetSelectedObject(objectData);
                                Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                                newPos.y = originalY;

                                if (targetGrid.TryPlaceObject(newPos))
                                {
                                    placementSuccessful = true;
                                    // EventManager ile nesne taşıma event'i gönder
                                    EventManager.Invoke("ObjectMovedBetweenGrids", draggedObject);
                                    Debug.Log("Nesne başarıyla taşındı: " + draggedObject.name);
                                }
                            }
                        }
                        else
                        {
                            // Aynı grid içinde hareket
                            if (targetGrid.TryMoveObjectWithinGrid(originalGridPosition, targetGridPos))
                            {
                                Vector3 newPos = targetGrid.GridToWorldPosition(targetGridPos);
                                newPos.y = originalY;
                                draggedObject.transform.position = newPos;
                                placementSuccessful = true;
                                // EventManager ile aynı grid içinde taşıma event'i gönder
                                EventManager.Invoke("ObjectMovedWithinGrid", draggedObject);
                                Debug.Log("Nesne aynı grid içinde taşındı");
                            }
                        }
                    }
                }

                // Eğer yerleştirme başarısız olduysa orijinal pozisyona geri koy
                if (!placementSuccessful)
                {
                    Vector3 originalPos = originalGrid.GridToWorldPosition(originalGridPosition);
                    originalPos.y = draggedObject.transform.position.y;
                    draggedObject.transform.position = originalPos;
                    Debug.Log("Nesne orijinal pozisyonuna geri döndürüldü");
                }

                draggedObject = null;
                originalGrid = null;
            }
            else if (isDeleteMode && draggedObject != null)
            {
                // Silme modunda draggedObject'i temizle
                draggedObject = null;
                originalGrid = null;
            }
            else
            {
                // Original click handling
                float dragDistance = Vector2.Distance(dragStartPosition, Input.mousePosition);
                bool deleteMode = placementUI != null && placementUI.IsDeleteModeActive();

                Debug.Log($"Click handling - DragDistance: {dragDistance}, Threshold: {dragThreshold}, IsPanning: {cameraSetup.IsPanning}, DeleteMode: {deleteMode}");

                if (dragDistance < dragThreshold && !cameraSetup.IsPanning)
                {
                    if (deleteMode)
                    {
                        Debug.Log("Calling TryDeleteObject");
                        TryDeleteObject();
                    }
                    else
                    {
                        Debug.Log("Calling TryPlaceObject");
                        TryPlaceObject();
                    }
                }
                else
                {
                    Debug.Log($"Click ignored - DragDistance: {dragDistance} >= {dragThreshold} OR IsPanning: {cameraSetup.IsPanning}");
                }
            }

            isDragging = false;
        }
    }

    private bool IsPointerOverUI()
    {
        // Touch kontrolü
        if (Input.touchCount > 0)
        {
            return EventSystem.current.IsPointerOverGameObject(Input.GetTouch(0).fingerId);
        }
        
        // Mouse kontrolü
        return EventSystem.current.IsPointerOverGameObject();
    }

    private void TryPlaceObject()
    {
        Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
        {
            // EventManager ile nesne yerleştirme event'i gönder
            EventManager.Invoke("TryPlaceObject", hit.point);
        }
    }

    private void HandleHover()
    {
        if (placementUI != null && placementUI.IsDeleteModeActive())
        {
            Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);

            if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
            {
                // Hangi grid sistemine hover yapıldığını bul
                GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
                if (hitGrid != null)
                {
                    Vector2Int gridPos = hitGrid.GetGridPositionFromWorld(hit.point);
                    PooledObject hoverObj = hitGrid.GetObjectAtCell(gridPos);

                    if (hoverObj != currentHoverObject)
                    {
                        // Önceki hover'ı temizle
                        if (currentHoverObject != null)
                        {
                            EventManager.Invoke("ObjectHoverEnd", currentHoverObject);
                        }

                        currentHoverObject = hoverObj;

                        // Yeni hover'ı başlat
                        if (currentHoverObject != null)
                        {
                            EventManager.Invoke("ObjectHoverStart", currentHoverObject);
                            currentHoverObject.SetHighlight(true);
                            Debug.Log($"Highlight set for: {currentHoverObject.name}");
                        }
                    }
                }
                else
                {
                    Debug.Log("No GridPlacementSystem found on hit collider");
                    ClearHover();
                }
            }
            else
            {
                ClearHover();
            }
        }
        else
        {
            ClearHover();
        }
    }

    private void ClearHover()
    {
        if (currentHoverObject != null)
        {
            EventManager.Invoke("ObjectHoverEnd", currentHoverObject);
            currentHoverObject.SetHighlight(false);
            currentHoverObject = null;
        }
    }

    private void TryDeleteObject()
    {
        Debug.Log($"TryDeleteObject called. CurrentHoverObject: {(currentHoverObject != null ? currentHoverObject.name : "null")}");

        if (currentHoverObject != null)
        {
            // Nesnenin hangi grid'de olduğunu object data'dan öğren
            PlaceableObjectData objectData = currentHoverObject.GetObjectData();
            GridPlacementSystem targetGrid = null;

            Debug.Log($"ObjectData: {(objectData != null ? objectData.objectName : "null")}, CurrentGrid: {(objectData?.currentGrid != null ? objectData.currentGrid.name : "null")}");

            if (objectData != null && objectData.currentGrid != null)
            {
                targetGrid = objectData.currentGrid;
                Debug.Log($"Using currentGrid: {targetGrid.name}");
            }
            else
            {
                // Fallback: raycast ile grid'i bul
                Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
                if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
                {
                    targetGrid = hit.collider.GetComponent<GridPlacementSystem>();
                    Debug.Log($"Using raycast grid: {(targetGrid != null ? targetGrid.name : "null")}");
                }
            }

            if (targetGrid != null)
            {
                Vector3 worldPos = currentHoverObject.transform.position;
                // EventManager ile nesne silme event'i gönder
                EventManager.Invoke("TryRemoveObject", worldPos);
                Debug.Log($"Remove event sent for: {currentHoverObject.name}, Grid: {targetGrid.name}, WorldPos: {worldPos}");
            }
            else
            {
                Debug.LogError("TargetGrid is null! Cannot delete object.");
            }

            ClearHover();
        }
        else
        {
            Debug.LogWarning("No hover object to delete!");
        }
    }

    // Multi-place sistemi metodları
    private void StartMultiPlaceMode(GridPlacementSystem grid, Vector2Int startPos)
    {
        // Seçili objeyi grid'den al
        multiPlaceObjectData = grid.GetSelectedObject();

        if (multiPlaceObjectData == null)
        {
            Debug.LogWarning("Multi-place için seçili obje yok!");
            return;
        }

        isMultiPlaceMode = true;
        multiPlaceGrid = grid;
        multiPlacedCells.Clear();

        // EventManager ile multi-place başladı event'i gönder
        EventManager.Invoke("MultiPlaceStarted");

        // İlk pozisyona obje yerleştir
        TryPlaceObjectAtPosition(startPos);

        Debug.Log($"Multi-place modu başlatıldı. Obje: {multiPlaceObjectData.objectName}");
    }

    private void HandleMultiPlaceDrag()
    {
        if (multiPlaceGrid == null || multiPlaceObjectData == null) return;

        Ray ray = mainCamera.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, groundLayer))
        {
            GridPlacementSystem hitGrid = hit.collider.GetComponent<GridPlacementSystem>();
            if (hitGrid == multiPlaceGrid) // Aynı grid'de kalmalı
            {
                Vector2Int currentPos = hitGrid.GetGridPositionFromWorld(hit.point);
                TryPlaceObjectAtPosition(currentPos);
            }
        }
    }

    private void TryPlaceObjectAtPosition(Vector2Int gridPos)
    {
        if (multiPlaceGrid == null || multiPlaceObjectData == null) return;

        // Bu pozisyona daha önce yerleştirildi mi?
        if (multiPlacedCells.Contains(gridPos)) return;

        // Grid sınırları içinde mi?
        Vector2Int adjustedPosition = new Vector2Int(
            gridPos.x - (multiPlaceObjectData.size.x - 1) / 2,
            gridPos.y - (multiPlaceObjectData.size.y - 1) / 2
        );

        // Objeyi yerleştirmeyi dene
        multiPlaceGrid.SetSelectedObject(multiPlaceObjectData);
        Vector3 worldPos = multiPlaceGrid.GridToWorldPosition(gridPos);

        if (multiPlaceGrid.TryPlaceObject(worldPos))
        {
            // Başarılı yerleştirme - bu cell'i işaretle
            for (int x = 0; x < multiPlaceObjectData.size.x; x++)
            {
                for (int y = 0; y < multiPlaceObjectData.size.y; y++)
                {
                    Vector2Int occupiedPos = adjustedPosition + new Vector2Int(x, y);
                    multiPlacedCells.Add(occupiedPos);
                }
            }

            Debug.Log($"Multi-place: Obje yerleştirildi {gridPos}");
        }
    }

    private void EndMultiPlaceMode()
    {
        // EventManager ile multi-place bitti event'i gönder
        EventManager.Invoke("MultiPlaceEnded");

        isMultiPlaceMode = false;
        multiPlaceGrid = null;
        multiPlacedCells.Clear();

        Debug.Log("Multi-place modu sonlandırıldı");
    }


}
