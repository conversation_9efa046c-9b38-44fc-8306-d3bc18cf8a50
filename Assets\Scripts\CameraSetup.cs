using UnityEngine;
using UnityEngine.UI;
using GridSystem;

[ExecuteInEditMode]
public class CameraSetup : MonoBehaviour
{
    public GridManager gridManager;
    public float panSpeed = 0.01f;

    [Header("Mobile Touch Controls")]
    public float zoomSpeed = 2f;
    public float rotateSpeed = 100f;
    public float tiltSpeed = 50f;
    public float minZoom = 5f;
    public float maxZoom = 50f;
    public float minTilt = 10f;
    public float maxTilt = 80f;

    [Header("UI Controls")]
    public Slider panSpeedSlider;
    public Slider rotateSpeedSlider;
    public Slider zoomSpeedSlider;
    public Slider tiltSpeedSlider;

    private Vector2 lastPanPosition;
    public bool IsPanning { get; private set; }

    // Touch kontrolleri için değişkenler
    private float lastTouchDistance;
    private float lastTouchAngle;
    private Vector2 lastTouchCenter;
    private bool isTwoFingerGesture = false;

    private bool blockPanning = false;

    /// <summary>
    /// Sürükleme sırasında kamera kaymasını engelle.
    /// </summary>
    public void BlockPanningDuringDrag(bool block)
    {
        blockPanning = block;
    }

    void Start()
    {
        if (gridManager == null)
        {
            gridManager = FindObjectOfType<GridManager>();
        }
        SetupSliders();
        CenterCameraOnGrid();
    }

    void OnValidate()
    {
        if (gridManager != null)
        {
            CenterCameraOnGrid();
        }
    }

    void Update()
    {
        if (Application.isPlaying)
        {
            HandlePanning();
        }
        else
        {
            CenterCameraOnGrid();
        }
    }

    void HandlePanning()
    {
        if (blockPanning) return;

        // Dokunmatik kontrol
        if (Input.touchCount > 0)
        {
            if (Input.touchCount == 2)
            {
                HandleTwoFingerGestures();
            }
            else if (Input.touchCount == 1 && !isTwoFingerGesture)
            {
                HandleSingleTouch();
            }
        }
        else // Mouse kontrol
        {
            HandleMouseInput();
        }

        // İki parmak gesture bittiğinde flag'i sıfırla
        if (Input.touchCount < 2)
        {
            isTwoFingerGesture = false;
        }
    }

    void HandleSingleTouch()
    {
        Touch touch = Input.GetTouch(0);

        if (touch.phase == TouchPhase.Began)
        {
            IsPanning = true;
            lastPanPosition = touch.position;
        }
        else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
        {
            IsPanning = false;
        }
        else if (touch.phase == TouchPhase.Moved && IsPanning)
        {
            PanCamera(touch.position);
        }
    }

    void HandleMouseInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            IsPanning = true;
            lastPanPosition = Input.mousePosition;
        }
        else if (Input.GetMouseButtonUp(0))
        {
            IsPanning = false;
        }
        else if (Input.GetMouseButton(0) && IsPanning)
        {
            PanCamera(Input.mousePosition);
        }
    }

    void HandleTwoFingerGestures()
    {
        Touch touch1 = Input.GetTouch(0);
        Touch touch2 = Input.GetTouch(1);

        Vector2 touch1Pos = touch1.position;
        Vector2 touch2Pos = touch2.position;
        Vector2 touchCenter = (touch1Pos + touch2Pos) * 0.5f;

        float currentDistance = Vector2.Distance(touch1Pos, touch2Pos);
        float currentAngle = Mathf.Atan2(touch2Pos.y - touch1Pos.y, touch2Pos.x - touch1Pos.x) * Mathf.Rad2Deg;

        if (!isTwoFingerGesture)
        {
            // İlk frame - başlangıç değerlerini kaydet
            isTwoFingerGesture = true;
            lastTouchDistance = currentDistance;
            lastTouchAngle = currentAngle;
            lastTouchCenter = touchCenter;
            IsPanning = false; // Pan'i durdur
        }
        else
        {
            // Zoom (Pinch)
            float distanceDelta = currentDistance - lastTouchDistance;
            if (Mathf.Abs(distanceDelta) > 5f) // Minimum threshold
            {
                HandleZoom(distanceDelta);
            }

            // Rotate (Yatay)
            float angleDelta = Mathf.DeltaAngle(lastTouchAngle, currentAngle);
            if (Mathf.Abs(angleDelta) > 2f) // Minimum threshold
            {
                HandleRotate(angleDelta);
            }

            // Tilt (Dikey - 2 parmak yukarı/aşağı)
            Vector2 centerDelta = touchCenter - lastTouchCenter;
            if (Mathf.Abs(centerDelta.y) > 10f) // Minimum threshold
            {
                HandleTilt(centerDelta.y);
            }

            // Değerleri güncelle
            lastTouchDistance = currentDistance;
            lastTouchAngle = currentAngle;
            lastTouchCenter = touchCenter;
        }
    }

    void PanCamera(Vector2 newPosition)
    {
        Vector2 delta = newPosition - lastPanPosition;

        Vector3 movement = new Vector3(
            -delta.x * panSpeed,
            0f,
            -delta.y * panSpeed
        );

        // Kamera yönüne göre hareketi düzelt
        movement = Quaternion.Euler(0, transform.eulerAngles.y, 0) * movement;

        transform.position += movement;
        lastPanPosition = newPosition;
    }

    void HandleZoom(float distanceDelta)
    {
        // Zoom in/out - kamera yüksekliğini değiştir
        float zoomAmount = distanceDelta * zoomSpeed * Time.deltaTime;
        Vector3 currentPos = transform.position;

        float newY = Mathf.Clamp(currentPos.y - zoomAmount, minZoom, maxZoom);
        transform.position = new Vector3(currentPos.x, newY, currentPos.z);

        // Zoom sonrası grid merkezine tekrar odaklan
        CenterCameraOnGrid();
    }

    void HandleRotate(float angleDelta)
    {
        // Yatay rotasyon - Y ekseninde döndür
        float rotateAmount = angleDelta * rotateSpeed * Time.deltaTime;
        transform.Rotate(0, rotateAmount, 0, Space.World);

        // Rotasyon sonrası grid merkezine tekrar odaklan
        CenterCameraOnGrid();
    }

    void HandleTilt(float verticalDelta)
    {
        // Dikey tilt - X ekseninde döndür
        float tiltAmount = -verticalDelta * tiltSpeed * Time.deltaTime;
        Vector3 currentRotation = transform.eulerAngles;

        float newXRotation = currentRotation.x + tiltAmount;

        // X rotasyonunu normalize et (0-360 arasında)
        if (newXRotation > 180f) newXRotation -= 360f;

        newXRotation = Mathf.Clamp(newXRotation, minTilt, maxTilt);
        transform.rotation = Quaternion.Euler(newXRotation, currentRotation.y, currentRotation.z);

        // Tilt sonrası grid merkezine tekrar odaklan
        CenterCameraOnGrid();
    }

    void SetupSliders()
    {
        // Pan Speed Slider
        if (panSpeedSlider != null)
        {
            panSpeedSlider.minValue = 0.001f;
            panSpeedSlider.maxValue = 0.1f;
            panSpeedSlider.value = panSpeed;
            panSpeedSlider.onValueChanged.AddListener(OnPanSpeedChanged);
        }

        // Rotate Speed Slider
        if (rotateSpeedSlider != null)
        {
            rotateSpeedSlider.minValue = 10f;
            rotateSpeedSlider.maxValue = 500f;
            rotateSpeedSlider.value = rotateSpeed;
            rotateSpeedSlider.onValueChanged.AddListener(OnRotateSpeedChanged);
        }

        // Zoom Speed Slider
        if (zoomSpeedSlider != null)
        {
            zoomSpeedSlider.minValue = 0.5f;
            zoomSpeedSlider.maxValue = 10f;
            zoomSpeedSlider.value = zoomSpeed;
            zoomSpeedSlider.onValueChanged.AddListener(OnZoomSpeedChanged);
        }

        // Tilt Speed Slider
        if (tiltSpeedSlider != null)
        {
            tiltSpeedSlider.minValue = 10f;
            tiltSpeedSlider.maxValue = 200f;
            tiltSpeedSlider.value = tiltSpeed;
            tiltSpeedSlider.onValueChanged.AddListener(OnTiltSpeedChanged);
        }
    }

    // Slider Event Handlers
    void OnPanSpeedChanged(float value)
    {
        panSpeed = value;
    }

    void OnRotateSpeedChanged(float value)
    {
        rotateSpeed = value;
    }

    void OnZoomSpeedChanged(float value)
    {
        zoomSpeed = value;
    }

    void OnTiltSpeedChanged(float value)
    {
        tiltSpeed = value;
    }

    void OnDestroy()
    {
        // Slider listener'larını temizle
        if (panSpeedSlider != null)
            panSpeedSlider.onValueChanged.RemoveListener(OnPanSpeedChanged);
        if (rotateSpeedSlider != null)
            rotateSpeedSlider.onValueChanged.RemoveListener(OnRotateSpeedChanged);
        if (zoomSpeedSlider != null)
            zoomSpeedSlider.onValueChanged.RemoveListener(OnZoomSpeedChanged);
        if (tiltSpeedSlider != null)
            tiltSpeedSlider.onValueChanged.RemoveListener(OnTiltSpeedChanged);
    }

   void CenterCameraOnGrid()
{
    if (gridManager == null) return;

    // Grid'in merkez hücresinin dünya pozisyonu
    Vector2Int centerGridPos = new Vector2Int(
        (gridManager.gridSize.x - 1) / 2,
        (gridManager.gridSize.y - 1) / 2
    );

    Vector3 gridCenter = gridManager.transform.position + new Vector3(
        (centerGridPos.x + 0.5f) * gridManager.cellSize,
        0f,
        (centerGridPos.y + 0.5f) * gridManager.cellSize
    );

    // Kamera yön vektörünü al (ileriye doğru bakış yönü)
    Vector3 forward = transform.forward;
    forward.y = 0; // Y bileşenini yok say (kameranın ileri düzlemindeki yön)

    forward.Normalize();

    // Kamera yüksekliği sabit (12), bu yüzden o yükseklikten geriye doğru bakıyoruz
    float height = transform.position.y;
    float angleXRad = transform.eulerAngles.x * Mathf.Deg2Rad;

    // Kamera merkezdeki hücreye baksın diye, yüksekliği ve açıya göre offset hesapla
    float distance = height / Mathf.Tan(angleXRad); // Eğik üçgende taban

    // Grid merkezinden ileri doğru bakış yönünde uzaklık kadar geri çık
    Vector3 cameraPos = gridCenter - forward * distance;

    // Y sabit kalsın
    cameraPos.y = transform.position.y;

    transform.position = cameraPos;
}


}
