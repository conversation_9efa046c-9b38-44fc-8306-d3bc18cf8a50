using UnityEngine;
using UnityEngine.UI;
using GridSystem;

[ExecuteInEditMode]
public class CameraSetup : MonoBehaviour
{
    public GridManager gridManager;
    public float panSpeed = 0.01f;

    [Header("Mobile Touch Controls")]
    public float zoomSpeed = 2f;
    public float rotateSpeed = 100f;
    public float tiltSpeed = 50f;
    public float minZoom = 2f;   // Minimum orthographic size (max zoom in)
    public float maxZoom = 20f;  // Maximum orthographic size (max zoom out)
    public float minTilt = 10f;
    public float maxTilt = 80f;

    [Header("UI Controls")]
    public Slider panSpeedSlider;
    public Slider rotateSpeedSlider;
    public Slider zoomSpeedSlider;
    public Slider tiltSpeedSlider;

    private Vector2 lastPanPosition;
    public bool IsPanning { get; private set; }

    // Touch kontrolleri için değişkenler
    private float lastTouchDistance;
    private float lastTouchAngle;
    private Vector2 lastTouchCenter;
    private bool isTwoFingerGesture = false;

    // Gesture stabilizasyonu için
    private enum GestureType { None, Zoom, Rotate, Tilt }
    private GestureType currentGesture = GestureType.None;
    private float gestureStartTime;
    private const float gestureStabilizeTime = 0.05f; // 50ms stabilizasyon süresi (daha hızlı)
    private const float zoomThreshold = 5f;  // Zoom için daha düşük threshold
    private const float rotateThreshold = 3f; // Rotate için daha düşük threshold
    private const float tiltThreshold = 10f; // Tilt için daha düşük threshold

    private bool blockPanning = false;

    /// <summary>
    /// Sürükleme sırasında kamera kaymasını engelle.
    /// </summary>
    public void BlockPanningDuringDrag(bool block)
    {
        blockPanning = block;
    }

    void Start()
    {
        if (gridManager == null)
        {
            gridManager = FindObjectOfType<GridManager>();
        }
        SetupSliders();
        CenterCameraOnGrid();
    }

    void OnValidate()
    {
        if (gridManager != null)
        {
            CenterCameraOnGrid();
        }
    }

    void Update()
    {
        if (Application.isPlaying)
        {
            HandlePanning();
        }
        else
        {
            CenterCameraOnGrid();
        }
    }

    void HandlePanning()
    {
        if (blockPanning) return;

        // Dokunmatik kontrol
        if (Input.touchCount > 0)
        {
            if (Input.touchCount == 2)
            {
                HandleTwoFingerGestures();
            }
            else if (Input.touchCount == 1 && !isTwoFingerGesture)
            {
                HandleSingleTouch();
            }
        }
        else // Mouse kontrol
        {
            HandleMouseInput();
        }

        // İki parmak gesture bittiğinde flag'leri sıfırla
        if (Input.touchCount < 2)
        {
            isTwoFingerGesture = false;
            currentGesture = GestureType.None;
        }
    }

    void HandleSingleTouch()
    {
        Touch touch = Input.GetTouch(0);

        if (touch.phase == TouchPhase.Began)
        {
            IsPanning = true;
            lastPanPosition = touch.position;
        }
        else if (touch.phase == TouchPhase.Ended || touch.phase == TouchPhase.Canceled)
        {
            IsPanning = false;
        }
        else if (touch.phase == TouchPhase.Moved && IsPanning)
        {
            PanCamera(touch.position);
        }
    }

    void HandleMouseInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            IsPanning = true;
            lastPanPosition = Input.mousePosition;
        }
        else if (Input.GetMouseButtonUp(0))
        {
            IsPanning = false;
        }
        else if (Input.GetMouseButton(0) && IsPanning)
        {
            PanCamera(Input.mousePosition);
        }
    }

    void HandleTwoFingerGestures()
    {
        Touch touch1 = Input.GetTouch(0);
        Touch touch2 = Input.GetTouch(1);

        Vector2 touch1Pos = touch1.position;
        Vector2 touch2Pos = touch2.position;
        Vector2 touchCenter = (touch1Pos + touch2Pos) * 0.5f;

        float currentDistance = Vector2.Distance(touch1Pos, touch2Pos);
        float currentAngle = Mathf.Atan2(touch2Pos.y - touch1Pos.y, touch2Pos.x - touch1Pos.x) * Mathf.Rad2Deg;

        if (!isTwoFingerGesture)
        {
            // İlk frame - başlangıç değerlerini kaydet
            isTwoFingerGesture = true;
            lastTouchDistance = currentDistance;
            lastTouchAngle = currentAngle;
            lastTouchCenter = touchCenter;
            currentGesture = GestureType.None;
            gestureStartTime = Time.time;
            IsPanning = false; // Pan'i durdur
        }
        else
        {
            // Gesture değerlerini hesapla
            float distanceDelta = currentDistance - lastTouchDistance;
            float angleDelta = Mathf.DeltaAngle(lastTouchAngle, currentAngle);
            Vector2 centerDelta = touchCenter - lastTouchCenter;

            // Stabilizasyon süresi geçtiyse dominant gesture'ı belirle
            if (currentGesture == GestureType.None && Time.time - gestureStartTime > gestureStabilizeTime)
            {
                currentGesture = DetermineGesture(distanceDelta, angleDelta, centerDelta.y);
            }

            // Sadece belirlenen gesture'ı işle
            switch (currentGesture)
            {
                case GestureType.Zoom:
                    if (Mathf.Abs(distanceDelta) > 1f) // Daha düşük threshold
                        HandleZoom(distanceDelta);
                    break;

                case GestureType.Rotate:
                    if (Mathf.Abs(angleDelta) > 0.5f) // Daha düşük threshold
                        HandleRotate(angleDelta);
                    break;

                case GestureType.Tilt:
                    if (Mathf.Abs(centerDelta.y) > 2f) // Daha düşük threshold
                        HandleTilt(centerDelta.y);
                    break;
            }

            // Değerleri güncelle
            lastTouchDistance = currentDistance;
            lastTouchAngle = currentAngle;
            lastTouchCenter = touchCenter;
        }
    }

    GestureType DetermineGesture(float distanceDelta, float angleDelta, float verticalDelta)
    {
        float absDistance = Mathf.Abs(distanceDelta);
        float absAngle = Mathf.Abs(angleDelta);
        float absVertical = Mathf.Abs(verticalDelta);

        // En baskın hareketi belirle
        if (absDistance > zoomThreshold && absDistance > absAngle && absDistance > absVertical * 0.5f)
        {
            return GestureType.Zoom;
        }
        else if (absAngle > rotateThreshold && absAngle > absDistance * 0.1f && absAngle > absVertical * 0.1f)
        {
            return GestureType.Rotate;
        }
        else if (absVertical > tiltThreshold && absVertical > absDistance * 0.5f && absVertical > absAngle)
        {
            return GestureType.Tilt;
        }

        return GestureType.None;
    }

    void PanCamera(Vector2 newPosition)
    {
        Vector2 delta = newPosition - lastPanPosition;

        Vector3 movement = new Vector3(
            -delta.x * panSpeed,
            0f,
            -delta.y * panSpeed
        );

        // Kamera yönüne göre hareketi düzelt
        movement = Quaternion.Euler(0, transform.eulerAngles.y, 0) * movement;

        transform.position += movement;
        lastPanPosition = newPosition;
    }

    void HandleZoom(float distanceDelta)
    {
        // Zoom in/out - orthographic size'ı değiştir
        Camera cam = Camera.main;
        if (cam != null && cam.orthographic)
        {
            float zoomAmount = -distanceDelta * zoomSpeed * 0.01f; // Negatif çünkü size küçülürse zoom in
            float newSize = Mathf.Clamp(cam.orthographicSize + zoomAmount, minZoom, maxZoom);
            cam.orthographicSize = newSize;
        }
    }

    void HandleRotate(float angleDelta)
    {
        // Yatay rotasyon - Y ekseninde döndür
        // angleDelta zaten frame-based, Time.deltaTime gereksiz
        float rotateAmount = angleDelta * rotateSpeed * 0.01f; // Scaling factor
        transform.Rotate(0, rotateAmount, 0, Space.World);

        // Rotasyon sonrası grid merkezine tekrar odaklan
        CenterCameraOnGrid();
    }

    void HandleTilt(float verticalDelta)
    {
        // Dikey tilt - X ekseninde döndür
        // verticalDelta zaten frame-based, Time.deltaTime gereksiz
        float tiltAmount = -verticalDelta * tiltSpeed * 0.001f; // Scaling factor (daha küçük çünkü pixel değeri)
        Vector3 currentRotation = transform.eulerAngles;

        float newXRotation = currentRotation.x + tiltAmount;

        // X rotasyonunu normalize et (0-360 arasında)
        if (newXRotation > 180f) newXRotation -= 360f;

        newXRotation = Mathf.Clamp(newXRotation, minTilt, maxTilt);
        transform.rotation = Quaternion.Euler(newXRotation, currentRotation.y, currentRotation.z);

        // Tilt sonrası grid merkezine tekrar odaklan
        CenterCameraOnGrid();
    }

    void SetupSliders()
    {
        // Pan Speed Slider
        if (panSpeedSlider != null)
        {
            panSpeedSlider.minValue = 0.001f;
            panSpeedSlider.maxValue = 0.1f;
            panSpeedSlider.value = panSpeed;
            panSpeedSlider.onValueChanged.AddListener(OnPanSpeedChanged);
        }

        // Rotate Speed Slider
        if (rotateSpeedSlider != null)
        {
            rotateSpeedSlider.minValue = 10f;
            rotateSpeedSlider.maxValue = 500f;
            rotateSpeedSlider.value = rotateSpeed;
            rotateSpeedSlider.onValueChanged.AddListener(OnRotateSpeedChanged);
        }

        // Zoom Speed Slider
        if (zoomSpeedSlider != null)
        {
            zoomSpeedSlider.minValue = 0.1f;  // Daha yavaş zoom için
            zoomSpeedSlider.maxValue = 5f;    // Daha hızlı zoom için
            zoomSpeedSlider.value = zoomSpeed;
            zoomSpeedSlider.onValueChanged.AddListener(OnZoomSpeedChanged);
        }

        // Tilt Speed Slider
        if (tiltSpeedSlider != null)
        {
            tiltSpeedSlider.minValue = 10f;
            tiltSpeedSlider.maxValue = 200f;
            tiltSpeedSlider.value = tiltSpeed;
            tiltSpeedSlider.onValueChanged.AddListener(OnTiltSpeedChanged);
        }
    }

    // Slider Event Handlers
    void OnPanSpeedChanged(float value)
    {
        panSpeed = value;
    }

    void OnRotateSpeedChanged(float value)
    {
        rotateSpeed = value;
    }

    void OnZoomSpeedChanged(float value)
    {
        zoomSpeed = value;
    }

    void OnTiltSpeedChanged(float value)
    {
        tiltSpeed = value;
    }

    void OnDestroy()
    {
        // Slider listener'larını temizle
        if (panSpeedSlider != null)
            panSpeedSlider.onValueChanged.RemoveListener(OnPanSpeedChanged);
        if (rotateSpeedSlider != null)
            rotateSpeedSlider.onValueChanged.RemoveListener(OnRotateSpeedChanged);
        if (zoomSpeedSlider != null)
            zoomSpeedSlider.onValueChanged.RemoveListener(OnZoomSpeedChanged);
        if (tiltSpeedSlider != null)
            tiltSpeedSlider.onValueChanged.RemoveListener(OnTiltSpeedChanged);
    }

   void CenterCameraOnGrid()
{
    if (gridManager == null) return;

    // Grid'in merkez hücresinin dünya pozisyonu
    Vector2Int centerGridPos = new Vector2Int(
        (gridManager.gridSize.x - 1) / 2,
        (gridManager.gridSize.y - 1) / 2
    );

    Vector3 gridCenter = gridManager.transform.position + new Vector3(
        (centerGridPos.x + 0.5f) * gridManager.cellSize,
        0f,
        (centerGridPos.y + 0.5f) * gridManager.cellSize
    );

    // Kamera yön vektörünü al (ileriye doğru bakış yönü)
    Vector3 forward = transform.forward;
    forward.y = 0; // Y bileşenini yok say (kameranın ileri düzlemindeki yön)

    forward.Normalize();

    // Kamera yüksekliği sabit (12), bu yüzden o yükseklikten geriye doğru bakıyoruz
    float height = transform.position.y;
    float angleXRad = transform.eulerAngles.x * Mathf.Deg2Rad;

    // Kamera merkezdeki hücreye baksın diye, yüksekliği ve açıya göre offset hesapla
    float distance = height / Mathf.Tan(angleXRad); // Eğik üçgende taban

    // Grid merkezinden ileri doğru bakış yönünde uzaklık kadar geri çık
    Vector3 cameraPos = gridCenter - forward * distance;

    // Y sabit kalsın
    cameraPos.y = transform.position.y;

    transform.position = cameraPos;
}


}
